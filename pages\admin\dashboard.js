import Head from 'next/head'
import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'

export default function AdminDashboard() {
  const router = useRouter()
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [codes, setCodes] = useState([])
  const [stats, setStats] = useState({})
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [newCodeForm, setNewCodeForm] = useState({
    description: '',
    expiresInDays: 30
  })
  const [isCreating, setIsCreating] = useState(false)

  // 检查认证状态
  useEffect(() => {
    checkAuth()
  }, [])

  // 加载激活码数据
  useEffect(() => {
    if (isAuthenticated) {
      loadActivationCodes()
    }
  }, [isAuthenticated])

  const checkAuth = async () => {
    try {
      const response = await fetch('/api/admin/auth')
      const result = await response.json()
      
      if (result.authenticated) {
        setIsAuthenticated(true)
      } else {
        router.push('/admin/login')
      }
    } catch (error) {
      router.push('/admin/login')
    } finally {
      setIsLoading(false)
    }
  }

  const loadActivationCodes = async () => {
    try {
      const response = await fetch('/api/admin/activation-codes')
      if (response.ok) {
        const data = await response.json()
        setCodes(data.codes)
        setStats(data.stats)
      }
    } catch (error) {
      console.error('Failed to load activation codes:', error)
    }
  }

  const handleCreateCode = async (e) => {
    e.preventDefault()
    setIsCreating(true)

    try {
      const response = await fetch('/api/admin/activation-codes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newCodeForm),
      })

      if (response.ok) {
        setNewCodeForm({ description: '', expiresInDays: 30 })
        setShowCreateForm(false)
        loadActivationCodes() // 重新加载数据
      } else {
        alert('创建激活码失败')
      }
    } catch (error) {
      alert('创建激活码失败')
    } finally {
      setIsCreating(false)
    }
  }

  const handleToggleCode = async (codeId, action) => {
    try {
      const response = await fetch('/api/admin/activation-codes', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ codeId, action }),
      })

      if (response.ok) {
        loadActivationCodes() // 重新加载数据
      } else {
        alert('操作失败')
      }
    } catch (error) {
      alert('操作失败')
    }
  }

  const handleDeleteCode = async (codeId) => {
    if (!confirm('确定要删除这个激活码吗？')) {
      return
    }

    try {
      const response = await fetch('/api/admin/activation-codes', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ codeId }),
      })

      if (response.ok) {
        loadActivationCodes() // 重新加载数据
      } else {
        alert('删除失败')
      }
    } catch (error) {
      alert('删除失败')
    }
  }

  const handleLogout = async () => {
    try {
      await fetch('/api/admin/auth', { method: 'DELETE' })
      router.push('/admin/login')
    } catch (error) {
      router.push('/admin/login')
    }
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('zh-CN')
  }

  const getStatusBadge = (code) => {
    if (code.isUsed) return { text: '已使用', class: 'used' }
    if (!code.isActive) return { text: '已禁用', class: 'disabled' }
    if (new Date() > new Date(code.expiresAt)) return { text: '已过期', class: 'expired' }
    return { text: '可用', class: 'available' }
  }

  if (isLoading) {
    return (
      <div className="loading-container">
        <div className="spinner"></div>
        <p>加载中...</p>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null // 会被重定向到登录页
  }

  return (
    <>
      <Head>
        <title>激活码管理 - Great Heights School</title>
      </Head>
      <div className="container">
        {/* 主要内容卡片 */}
        <div className="main-card">
          <header className="header">
            <div className="header-content">
              <div className="header-left">
                <div className="logo-section">
                  <div className="logo-icon">🎓</div>
                  <div className="title-section">
                    <h1>激活码管理系统</h1>
                    <p className="subtitle">Great Heights School</p>
                  </div>
                </div>
              </div>
              <div className="header-right">
                <div className="header-actions">
                  <button
                    onClick={() => setShowCreateForm(true)}
                    className="btn btn-primary"
                  >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 5v14m-7-7h14" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    生成激活码
                  </button>
                  <button
                    onClick={handleLogout}
                    className="btn btn-secondary"
                  >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5z" fill="currentColor"/>
                      <path d="M4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z" fill="currentColor"/>
                    </svg>
                    登出
                  </button>
                </div>
              </div>
            </div>
          </header>

          <main className="main-content">

          <div className="stats-section">
            <h2>数据概览</h2>
            <div className="stats-grid">
              <div className="stat-card">
                <div className="stat-header">
                  <div className="stat-icon total">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M9 11H7v8h2v-8zm4-4h-2v12h2V7zm4-2h-2v14h2V5z" fill="currentColor"/>
                    </svg>
                  </div>
                  <h3>总计</h3>
                </div>
                <div className="stat-number">{stats.total || 0}</div>
                <div className="stat-desc">激活码总数</div>
              </div>
              <div className="stat-card">
                <div className="stat-header">
                  <div className="stat-icon available">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" fill="currentColor"/>
                    </svg>
                  </div>
                  <h3>可用</h3>
                </div>
                <div className="stat-number available">{stats.available || 0}</div>
                <div className="stat-desc">可正常使用</div>
              </div>
              <div className="stat-card">
                <div className="stat-header">
                  <div className="stat-icon used">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="currentColor"/>
                    </svg>
                  </div>
                  <h3>已使用</h3>
                </div>
                <div className="stat-number used">{stats.used || 0}</div>
                <div className="stat-desc">已被激活</div>
              </div>
              <div className="stat-card">
                <div className="stat-header">
                  <div className="stat-icon expired">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-6V7h2v4h4v2z" fill="currentColor"/>
                    </svg>
                  </div>
                  <h3>已过期</h3>
                </div>
                <div className="stat-number expired">{stats.expired || 0}</div>
                <div className="stat-desc">超过有效期</div>
              </div>
            </div>
          </div>

        {showCreateForm && (
          <div className="modal-overlay" onClick={() => setShowCreateForm(false)}>
            <div className="modal" onClick={(e) => e.stopPropagation()}>
              <h2>生成新激活码</h2>
              <form onSubmit={handleCreateCode}>
                <div className="form-group">
                  <label>描述</label>
                  <input
                    type="text"
                    value={newCodeForm.description}
                    onChange={(e) => setNewCodeForm({...newCodeForm, description: e.target.value})}
                    placeholder="激活码用途描述（可选）"
                  />
                </div>
                <div className="form-group">
                  <label>有效期（天）</label>
                  <input
                    type="number"
                    value={newCodeForm.expiresInDays}
                    onChange={(e) => setNewCodeForm({...newCodeForm, expiresInDays: parseInt(e.target.value)})}
                    min="1"
                    max="365"
                    required
                  />
                </div>
                <div className="form-actions">
                  <button 
                    type="button" 
                    onClick={() => setShowCreateForm(false)}
                    className="btn btn-secondary"
                  >
                    取消
                  </button>
                  <button 
                    type="submit" 
                    disabled={isCreating}
                    className="btn btn-primary"
                  >
                    {isCreating ? '生成中...' : '生成'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

          <div className="codes-section">
            <div className="section-header">
              <h2>激活码列表</h2>
              <div className="section-actions">
                <div className="search-box">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="m21 21-6-6m2-5a7 7 0 1 1-14 0 7 7 0 0 1 14 0z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  <input type="text" placeholder="搜索激活码..." />
                </div>
              </div>
            </div>
            {codes.length === 0 ? (
              <div className="empty-state">
                <div className="empty-icon">
                  <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 11H7v8h2v-8zm4-4h-2v12h2V7zm4-2h-2v14h2V5z" fill="currentColor"/>
                  </svg>
                </div>
                <h3>暂无激活码</h3>
                <p>点击"生成激活码"按钮创建第一个激活码</p>
              </div>
            ) : (
              <div className="codes-table">
                <div className="table-header">
                  <div className="col-code">激活码</div>
                  <div className="col-status">状态</div>
                  <div className="col-created">创建时间</div>
                  <div className="col-expires">过期时间</div>
                  <div className="col-user">使用者</div>
                  <div className="col-actions">操作</div>
                </div>
              {codes.map((code) => {
                const status = getStatusBadge(code)
                return (
                  <div key={code.id} className="table-row">
                    <div className="code-cell">
                      <span className="code-text">{code.code}</span>
                      {code.description && (
                        <span className="code-desc">{code.description}</span>
                      )}
                    </div>
                    <div>
                      <span className={`status-badge ${status.class}`}>
                        {status.text}
                      </span>
                    </div>
                    <div>{formatDate(code.createdAt)}</div>
                    <div>{formatDate(code.expiresAt)}</div>
                    <div>{code.usedBy || '-'}</div>
                    <div className="actions">
                      {!code.isUsed && (
                        <button
                          onClick={() => handleToggleCode(code.id, code.isActive ? 'disable' : 'enable')}
                          className={`btn btn-sm ${code.isActive ? 'btn-warning' : 'btn-success'}`}
                        >
                          {code.isActive ? '禁用' : '启用'}
                        </button>
                      )}
                      <button
                        onClick={() => handleDeleteCode(code.id)}
                        className="btn btn-sm btn-danger"
                      >
                        删除
                      </button>
                    </div>
                  </div>
                )
              })}
            </div>
          )}
          </div>
        </main>
        </div>

        {/* Footer */}
        <footer className="footer">
          <p>
            <span className="powered-by">
              <svg className="icon" width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
              </svg>
              Powered by{' '}
              <a href="https://www.ghs.red/" target="_blank" rel="noopener">
                Garbage Human Studio
              </a>
            </span>
          </p>
        </footer>
      </div>

      <style jsx>{`
        .loading-container {
          min-height: 100vh;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          background: #fafafa;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
        }
        .spinner {
          width: 32px;
          height: 32px;
          border: 3px solid #e1e5e9;
          border-top: 3px solid #111827;
          border-radius: 50%;
          animation: spin 0.8s linear infinite;
          margin-bottom: 16px;
        }
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        .container {
          min-height: 100vh;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          background: #fafafa;
          padding: 20px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
        }
        .main-card {
          background: #ffffff;
          max-width: 1200px;
          width: 100%;
          border-radius: 12px;
          border: 1px solid #e1e5e9;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          overflow: hidden;
        }
        .header {
          background: #ffffff;
          border-bottom: 1px solid #e1e5e9;
          padding: 20px 32px;
        }
        .header-content {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        .header-left {
          display: flex;
          align-items: center;
        }
        .header-right {
          display: flex;
          align-items: center;
        }
        .logo-section {
          display: flex;
          align-items: center;
          gap: 16px;
        }
        .logo-icon {
          font-size: 32px;
          line-height: 1;
        }
        .title-section h1 {
          color: #111827;
          margin: 0;
          font-size: 24px;
          font-weight: 600;
          letter-spacing: -0.02em;
          line-height: 1.2;
        }
        .subtitle {
          color: #6b7280;
          margin: 2px 0 0 0;
          font-size: 15px;
          font-weight: 400;
          line-height: 1.2;
        }
        .header-actions {
          display: flex;
          gap: 12px;
        }
        .main-content {
          padding: 32px;
        }
        .stats-section {
          margin-bottom: 48px;
        }
        .stats-section h2 {
          color: #111827;
          font-size: 20px;
          font-weight: 600;
          margin-bottom: 24px;
          letter-spacing: -0.02em;
        }
        .stats-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
          gap: 20px;
        }
        .stat-card {
          background: #ffffff;
          padding: 24px;
          border-radius: 12px;
          border: 1px solid #e1e5e9;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          transition: all 0.2s ease-out;
        }
        .stat-card:hover {
          border-color: #d1d5db;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }
        .stat-header {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 16px;
        }
        .stat-icon {
          width: 40px;
          height: 40px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .stat-icon.total {
          background: #f3f4f6;
          color: #374151;
        }
        .stat-icon.available {
          background: #ecfdf5;
          color: #16a34a;
        }
        .stat-icon.used {
          background: #eff6ff;
          color: #2563eb;
        }
        .stat-icon.expired {
          background: #fef2f2;
          color: #dc2626;
        }
        .stat-card h3 {
          margin: 0;
          color: #374151;
          font-size: 14px;
          font-weight: 500;
        }
        .stat-number {
          font-size: 28px;
          font-weight: 700;
          color: #111827;
          margin-bottom: 4px;
        }
        .stat-desc {
          color: #6b7280;
          font-size: 13px;
          margin: 0;
        }
        .stat-number.available { color: #16a34a; }
        .stat-number.used { color: #2563eb; }
        .stat-number.expired { color: #dc2626; }
        .codes-section {
          background: #ffffff;
          border-radius: 12px;
          border: 1px solid #e1e5e9;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .section-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 24px 24px 0 24px;
          margin-bottom: 24px;
        }
        .section-header h2 {
          margin: 0;
          color: #111827;
          font-size: 18px;
          font-weight: 600;
          letter-spacing: -0.02em;
        }
        .section-actions {
          display: flex;
          gap: 12px;
        }
        .search-box {
          position: relative;
          display: flex;
          align-items: center;
        }
        .search-box svg {
          position: absolute;
          left: 12px;
          color: #9ca3af;
          z-index: 1;
        }
        .search-box input {
          padding: 8px 12px 8px 36px;
          border: 1px solid #d1d5db;
          border-radius: 6px;
          font-size: 14px;
          width: 200px;
          transition: all 0.15s ease-out;
        }
        .search-box input:focus {
          outline: none;
          border-color: #111827;
          box-shadow: 0 0 0 3px rgba(17, 24, 39, 0.1);
        }
        .empty-state {
          text-align: center;
          padding: 64px 24px;
        }
        .empty-icon {
          margin: 0 auto 16px;
          width: 48px;
          height: 48px;
          color: #d1d5db;
        }
        .empty-state h3 {
          color: #111827;
          font-size: 16px;
          font-weight: 600;
          margin: 0 0 8px 0;
        }
        .empty-state p {
          color: #6b7280;
          font-size: 14px;
          margin: 0;
        }
        .codes-table {
          overflow-x: auto;
        }
        .table-header, .table-row {
          display: grid;
          grid-template-columns: 2fr 1fr 1.5fr 1.5fr 1.5fr 1fr;
          gap: 16px;
          padding: 16px 24px;
          border-bottom: 1px solid #f3f4f6;
          align-items: center;
        }
        .table-header {
          font-weight: 600;
          color: #374151;
          background: #f9fafb;
          font-size: 13px;
          text-transform: uppercase;
          letter-spacing: 0.05em;
          border-radius: 4px;
        }
        .code-cell {
          display: flex;
          flex-direction: column;
          gap: 4px;
        }
        .code-text {
          font-family: monospace;
          font-weight: 600;
          color: #333;
        }
        .code-desc {
          font-size: 12px;
          color: #666;
        }
        .status-badge {
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          font-weight: 500;
        }
        .status-badge.available {
          background: #d4edda;
          color: #155724;
        }
        .status-badge.used {
          background: #e2e3e5;
          color: #383d41;
        }
        .status-badge.disabled {
          background: #f8d7da;
          color: #721c24;
        }
        .status-badge.expired {
          background: #fff3cd;
          color: #856404;
        }
        .actions {
          display: flex;
          gap: 8px;
        }
        .btn {
          display: inline-flex;
          align-items: center;
          gap: 6px;
          padding: 8px 16px;
          border: 1px solid transparent;
          border-radius: 6px;
          cursor: pointer;
          font-size: 14px;
          font-weight: 500;
          transition: all 0.15s ease-out;
          text-decoration: none;
        }
        .btn-primary {
          background: #111827;
          color: #ffffff;
          border-color: #111827;
        }
        .btn-primary:hover {
          background: #374151;
          border-color: #374151;
          transform: translateY(-1px);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .btn-secondary {
          background: #ffffff;
          color: #374151;
          border-color: #d1d5db;
        }
        .btn-secondary:hover {
          background: #f9fafb;
          border-color: #9ca3af;
          transform: translateY(-1px);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        .btn-success {
          background: #16a34a;
          color: #ffffff;
          border-color: #16a34a;
        }
        .btn-success:hover {
          background: #15803d;
          border-color: #15803d;
        }
        .btn-warning {
          background: #f59e0b;
          color: #ffffff;
          border-color: #f59e0b;
        }
        .btn-warning:hover {
          background: #d97706;
          border-color: #d97706;
        }
        .btn-danger {
          background: #dc2626;
          color: #ffffff;
          border-color: #dc2626;
        }
        .btn-danger:hover {
          background: #b91c1c;
          border-color: #b91c1c;
        }
        .btn-sm {
          padding: 4px 8px;
          font-size: 12px;
        }
        .modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0,0,0,0.5);
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 1000;
        }
        .modal {
          background: #fff;
          padding: 24px;
          border-radius: 8px;
          width: 90%;
          max-width: 400px;
        }
        .modal h2 {
          margin: 0 0 20px 0;
          color: #333;
        }
        .form-group {
          margin-bottom: 16px;
        }
        .form-group label {
          display: block;
          margin-bottom: 4px;
          color: #333;
          font-weight: 500;
        }
        .form-group input {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 14px;
          box-sizing: border-box;
        }
        .form-group input:focus {
          outline: none;
          border-color: #4285f4;
        }
        .form-actions {
          display: flex;
          gap: 12px;
          justify-content: flex-end;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
          .header-content {
            padding: 12px 16px;
            flex-direction: column;
            gap: 16px;
            align-items: stretch;
          }
          .logo {
            justify-content: center;
          }
          .header-actions {
            justify-content: center;
          }
          .main-content {
            padding: 16px;
          }
          .stats-grid {
            grid-template-columns: 1fr;
            gap: 16px;
          }
          .section-header {
            flex-direction: column;
            gap: 16px;
            align-items: stretch;
            padding: 16px 16px 0 16px;
          }
          .search-box input {
            width: 100%;
          }
          .table-header, .table-row {
            grid-template-columns: 1fr;
            gap: 8px;
            padding: 12px 16px;
          }
          .table-header > div, .table-row > div {
            display: flex;
            justify-content: space-between;
            align-items: center;
          }
          .table-header > div::before, .table-row > div::before {
            content: attr(data-label);
            font-weight: 600;
            color: #6b7280;
            font-size: 12px;
          }
          .col-code::before { content: "激活码"; }
          .col-status::before { content: "状态"; }
          .col-created::before { content: "创建时间"; }
          .col-expires::before { content: "过期时间"; }
          .col-user::before { content: "使用者"; }
          .col-actions::before { content: "操作"; }
        }

        /* Footer */
        .footer {
          margin-top: 32px;
          color: #9ca3af;
          font-size: 13px;
          text-align: center;
        }
        .powered-by {
          display: inline-flex;
          align-items: center;
          gap: 4px;
        }
        .icon {
          flex-shrink: 0;
        }
        .footer a {
          color: #6b7280;
          text-decoration: none;
          font-weight: 500;
          margin-left: 4px;
        }
        .footer a:hover {
          color: #374151;
          text-decoration: underline;
        }

        /* 响应式设计 */
        @media (max-width: 1023px) {
          .main-card {
            max-width: 100%;
            margin: 0;
            border-radius: 0;
            border-left: none;
            border-right: none;
          }
          .container {
            padding: 0;
            justify-content: flex-start;
          }
        }

        @media (max-width: 768px) {
          .header {
            padding: 16px 20px;
          }
          .header-content {
            flex-direction: column;
            gap: 16px;
            align-items: flex-start;
          }
          .header-actions {
            width: 100%;
            justify-content: space-between;
          }
          .main-content {
            padding: 20px;
          }
          .logo-icon {
            font-size: 24px;
          }
          .title-section h1 {
            font-size: 20px;
          }
          .subtitle {
            font-size: 13px;
          }
        }

        @media (max-width: 480px) {
          .header {
            padding: 12px 16px;
          }
          .main-content {
            padding: 16px;
          }
          .stats-grid {
            grid-template-columns: 1fr;
            gap: 16px;
          }
          .stat-card {
            padding: 20px;
          }
          .btn {
            padding: 8px 12px;
            font-size: 13px;
          }
          .header-actions {
            flex-direction: column;
            gap: 8px;
          }
        }
      `}</style>
    </>
  )
}
